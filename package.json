{"name": "geonorm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "server": "tsx backend/index.ts", "server:dev": "nodemon --exec tsx backend/index.ts", "start:all": "concurrently \"npm run server:dev\" \"npm run dev\""}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "lucide-react": "^0.542.0", "multer": "^1.4.5-lts.1", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/multer": "^1.4.12", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.1", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "nodemon": "^3.1.10", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tsx": "^4.20.5", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}