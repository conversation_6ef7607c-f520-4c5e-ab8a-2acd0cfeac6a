/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './frontend/index.html',
        './frontend/**/*.{js,ts,jsx,tsx}',
    ],
    theme: {
        extend: {
            colors: {
                border: 'hsl(214.3 31.8% 91.4%)',
                input: 'hsl(214.3 31.8% 91.4%)',
                ring: 'hsl(221.2 83.2% 53.3%)',
                background: 'hsl(0 0% 100%)',
                foreground: 'hsl(222.2 84% 4.9%)',
                primary: {
                    DEFAULT: 'hsl(221.2 83.2% 53.3%)',
                    foreground: 'hsl(210 40% 98%)',
                },
                secondary: {
                    DEFAULT: 'hsl(210 40% 96%)',
                    foreground: 'hsl(222.2 84% 4.9%)',
                },
                muted: {
                    DEFAULT: 'hsl(210 40% 96%)',
                    foreground: 'hsl(215.4 16.3% 46.9%)',
                },
                card: {
                    DEFAULT: 'hsl(0 0% 100%)',
                    foreground: 'hsl(222.2 84% 4.9%)',
                },
            },
            borderRadius: {
                lg: '0.5rem',
                md: '0.375rem',
                sm: '0.25rem',
            },
        },
    },
    plugins: [],
}
